import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ContactForm from '../ContactForm';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('ContactForm', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('renders contact form with default props', () => {
    render(<ContactForm />);
    
    expect(screen.getByText('Get in Touch')).toBeInTheDocument();
    expect(screen.getByText('Send us a message and we\'ll get back to you within 24 hours.')).toBeInTheDocument();
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Message')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Send Message' })).toBeInTheDocument();
  });

  it('renders contact form with custom props', () => {
    render(
      <ContactForm 
        title="Custom Title"
        description="Custom description"
      />
    );
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom description')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<ContactForm />);
    
    const submitButton = screen.getByRole('button', { name: 'Send Message' });
    
    // Button should be disabled when fields are empty
    expect(submitButton).toBeDisabled();
    
    // Fill in name only
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' }
    });
    expect(submitButton).toBeDisabled();
    
    // Fill in email
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' }
    });
    expect(submitButton).toBeDisabled();
    
    // Fill in message - now button should be enabled
    fireEvent.change(screen.getByLabelText('Message'), {
      target: { value: 'Test message' }
    });
    expect(submitButton).not.toBeDisabled();
  });

  it('submits form successfully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Contact submission received successfully' })
    });

    render(<ContactForm />);
    
    // Fill in form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' }
    });
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText('Message'), {
      target: { value: 'Test message' }
    });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'Send Message' }));
    
    // Check that fetch was called with correct data
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'John Doe',
          email: '<EMAIL>',
          message: 'Test message'
        }),
      });
    });
    
    // Check success message appears
    await waitFor(() => {
      expect(screen.getByText('Message Sent!')).toBeInTheDocument();
      expect(screen.getByText(/Thanks John Doe!/)).toBeInTheDocument();
    });
  });

  it('handles form submission error', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'Server error' })
    });

    render(<ContactForm />);
    
    // Fill in form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' }
    });
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText('Message'), {
      target: { value: 'Test message' }
    });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'Send Message' }));
    
    // Check error message appears
    await waitFor(() => {
      expect(screen.getByText('Server error')).toBeInTheDocument();
    });
  });

  it('allows sending another message after success', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Contact submission received successfully' })
    });

    render(<ContactForm />);
    
    // Fill and submit form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' }
    });
    fireEvent.change(screen.getByLabelText('Email Address'), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText('Message'), {
      target: { value: 'Test message' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Send Message' }));
    
    // Wait for success message
    await waitFor(() => {
      expect(screen.getByText('Message Sent!')).toBeInTheDocument();
    });
    
    // Click "Send Another Message"
    fireEvent.click(screen.getByRole('button', { name: 'Send Another Message' }));
    
    // Form should be back to initial state
    expect(screen.getByText('Get in Touch')).toBeInTheDocument();
    expect(screen.getByLabelText('Name')).toHaveValue('');
    expect(screen.getByLabelText('Email Address')).toHaveValue('');
    expect(screen.getByLabelText('Message')).toHaveValue('');
  });
});
